#!/usr/bin/env python3
"""
Volume Alert Service - Monitor volume spikes for watchlist symbols
"""

import asyncio
import logging
import threading
import time
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional
from collections import defaultdict, deque

from services.core.error_service import handle_service_errors
from services.core.base_service import BaseAlertService, BaseConfigurableService, singleton
from services.market.market_service import get_market_service
from utils.config import get_watchlist_symbols

logger = logging.getLogger(__name__)

class VolumeAlertService(BaseAlertService, BaseConfigurableService):
    """Service for monitoring volume spikes in watchlist symbols"""

    def __init__(self):
        BaseAlertService.__init__(self)
        BaseConfigurableService.__init__(self)

        self.market_service = get_market_service()

        # Volume history for MA calculation
        self.volume_history_h4 = defaultdict(lambda: deque(maxlen=20))  # Keep last 20 H4 candles
        self.volume_history_d1 = defaultdict(lambda: deque(maxlen=20))  # Keep last 20 D1 candles

        # Alert tracking by candle timestamp
        self.alerted_candles = {}  # {symbol_timeframe_threshold: candle_timestamp}

    def _get_config_section(self) -> str:
        """Return the config section name for this service"""
        return 'volume_alerts'

    def _load_service_config(self, config: Dict[str, Any]):
        """Load volume alert specific configuration"""
        self.ma_period = config.get('ma_period', 20)  # MA20
        self.thresholds = config.get('thresholds', [1.8, 2.2])  # Volume/MA ratios

        logger.info(f"Volume alert service configured: interval={self.monitoring_interval}s, thresholds={self.thresholds}")

    async def _monitor_iteration(self):
        """Override BaseAsyncService monitor iteration"""
        await self._check_volume_alerts()
        self._cleanup_old_candle_tracking()
    
    @handle_service_errors
    async def _check_volume_alerts(self):
        """Check for volume alerts on watchlist symbols"""
        try:
            watchlist_symbols = get_watchlist_symbols()
            if not watchlist_symbols:
                logger.warning("No watchlist symbols configured")
                return
            
            alerts = []
            
            for symbol in watchlist_symbols:
                # Check H4 timeframe
                h4_alert = await self._check_symbol_volume(symbol, '4h')
                if h4_alert:
                    alerts.append(h4_alert)
                
                # Check D1 timeframe
                d1_alert = await self._check_symbol_volume(symbol, '1d')
                if d1_alert:
                    alerts.append(d1_alert)
                
                # Small delay between symbols to avoid rate limits
                await asyncio.sleep(0.5)
            
            # Send alerts if any
            if alerts:
                await self.send_alerts('volume_spike', {'alerts': alerts})
                
        except Exception as e:
            logger.error(f"Error checking volume alerts: {e}")
    
    async def _check_symbol_volume(self, symbol: str, timeframe: str) -> Optional[Dict[str, Any]]:
        """Check volume for a specific symbol and timeframe"""
        try:
            # Fetch OHLCV data
            df = self.market_service._fetch_ohlcv_data(symbol, timeframe, limit=21)
            if df is None or df.empty or len(df) < 2:
                return None

            # Get current candle data
            current_candle = df.iloc[-1]
            current_volume = float(current_candle['volume'])
            current_timestamp = int(current_candle['timestamp'].timestamp() * 1000)  # Convert to milliseconds

            # Get historical volumes (exclude current candle)
            volumes = [float(vol) for vol in df['volume'].iloc[:-1]]
            
            # Update volume history
            history_key = f"{symbol}_{timeframe}"
            if timeframe == '4h':
                self.volume_history_h4[history_key].extend(volumes)
                volume_history = list(self.volume_history_h4[history_key])
            else:  # 1d
                self.volume_history_d1[history_key].extend(volumes)
                volume_history = list(self.volume_history_d1[history_key])
            
            # Calculate MA20 if we have enough data
            if len(volume_history) < self.ma_period:
                return None
            
            ma20_volume = sum(volume_history[-self.ma_period:]) / self.ma_period
            
            if ma20_volume == 0:
                return None
            
            # Calculate volume ratio
            volume_ratio = current_volume / ma20_volume
            
            # Check thresholds
            for threshold in sorted(self.thresholds, reverse=True):
                if volume_ratio >= threshold:
                    # Check if we already alerted for this candle and threshold
                    alert_key = f"{symbol}_{timeframe}_{threshold}"

                    if alert_key in self.alerted_candles:
                        if self.alerted_candles[alert_key] == current_timestamp:
                            continue  # Already alerted for this candle

                    # Record alert for this candle
                    self.alerted_candles[alert_key] = current_timestamp
                    
                    # Create alert data
                    alert_data = {
                        'symbol': symbol,
                        'timeframe': timeframe,
                        'current_volume': current_volume,
                        'ma20_volume': ma20_volume,
                        'volume_ratio': volume_ratio,
                        'threshold': threshold,
                        'timestamp': time.time(),
                        'candle_timestamp': current_timestamp,
                        'price': await self.market_service.get_price(symbol)
                    }
                    
                    logger.info(f"Volume alert: {symbol} {timeframe} - {volume_ratio:.2f}x MA20 (threshold: {threshold}x) [Candle: {current_timestamp}]")
                    return alert_data
            
            return None
            
        except Exception as e:
            logger.error(f"Error checking volume for {symbol} {timeframe}: {e}")
            return None
    


    def _cleanup_old_candle_tracking(self):
        """Clean up old candle tracking data to prevent memory leak"""
        try:
            current_time = time.time()
            # Keep tracking data for last 24 hours (86400 seconds)
            cutoff_time = (current_time - 86400) * 1000  # Convert to milliseconds

            keys_to_remove = []
            for key, candle_timestamp in self.alerted_candles.items():
                if candle_timestamp < cutoff_time:
                    keys_to_remove.append(key)

            for key in keys_to_remove:
                del self.alerted_candles[key]

            if keys_to_remove:
                logger.debug(f"Cleaned up {len(keys_to_remove)} old candle tracking entries")

        except Exception as e:
            logger.error(f"Error cleaning up candle tracking: {e}")

# Singleton instance using decorator
@singleton
class VolumeAlertServiceSingleton(VolumeAlertService):
    pass

def get_volume_alert_service() -> VolumeAlertService:
    """Get singleton instance of VolumeAlertService"""
    return VolumeAlertServiceSingleton()
