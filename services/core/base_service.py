import asyncio
import logging
import threading
from abc import ABC, abstractmethod
from typing import Optional, Any, Dict, List, Callable

logger = logging.getLogger(__name__)

class BaseAsyncService(ABC):
    """
    Base class for async services with common monitoring patterns.
    Provides standardized start/stop monitoring functionality.
    """
    
    def __init__(self):
        self.is_running = False
        self.monitor_task: Optional[asyncio.Task] = None
        self.enabled = True
        self.monitoring_interval = 300  # 5 minutes default
        
    async def start_monitoring(self):
        """Start the monitoring service"""
        if self.is_running or not self.enabled:
            if not self.enabled:
                logger.info(f"{self.__class__.__name__} is disabled, skipping start")
            else:
                logger.warning(f"{self.__class__.__name__} is already running")
            return
            
        self.is_running = True
        self.monitor_task = asyncio.create_task(self._monitor_loop())
        logger.info(f"🟢 {self.__class__.__name__} monitoring started")
    
    async def stop_monitoring(self):
        """Stop the monitoring service"""
        if not self.is_running:
            return
            
        self.is_running = False
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
        logger.info(f"🔴 {self.__class__.__name__} monitoring stopped")
    
    async def _monitor_loop(self):
        """Main monitoring loop - calls _monitor_iteration() periodically"""
        while self.is_running:
            try:
                await self._monitor_iteration()
                await asyncio.sleep(self.monitoring_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in {self.__class__.__name__} monitor loop: {e}")
                await asyncio.sleep(60)  # Wait 1 minute before retry
    
    @abstractmethod
    async def _monitor_iteration(self):
        """Override this method to implement monitoring logic"""
        pass


class BaseAlertService(BaseAsyncService):
    """
    Base class for alert services with callback registration and cooldown mechanisms.
    """
    
    def __init__(self):
        super().__init__()
        self.alert_callbacks: List[Callable] = []
        self.alert_cooldowns: Dict[str, float] = {}
        self.default_cooldown = 3600  # 1 hour default
        
    def register_alert_callback(self, callback: Callable):
        """Register callback for alerts"""
        self.alert_callbacks.append(callback)
        logger.info(f"{self.__class__.__name__} alert callback registered")
    
    def should_send_alert(self, alert_key: str, cooldown_seconds: Optional[int] = None) -> bool:
        """Check if alert should be sent based on cooldown mechanism"""
        cooldown = cooldown_seconds or self.default_cooldown
        current_time = asyncio.get_event_loop().time()
        
        if alert_key in self.alert_cooldowns:
            last_alert_time = self.alert_cooldowns[alert_key]
            if current_time - last_alert_time < cooldown:
                return False
        
        # Update last alert time
        self.alert_cooldowns[alert_key] = current_time
        return True
    
    async def send_alerts(self, alert_type: str, alert_data: Any):
        """Send alerts to all registered callbacks"""
        try:
            for callback in self.alert_callbacks:
                await callback(alert_type, alert_data)
        except Exception as e:
            logger.error(f"Error sending alerts from {self.__class__.__name__}: {e}")


class BaseConfigurableService(ABC):
    """
    Base class for services that load configuration from config.yaml
    """
    
    def __init__(self):
        self._config_section = self._get_config_section()
        self._load_config()
    
    @abstractmethod
    def _get_config_section(self) -> str:
        """Return the config section name for this service"""
        pass
    
    def _load_config(self):
        """Load configuration from config.yaml"""
        from utils.config import load_config
        
        config = load_config()
        service_config = config.get(self._config_section, {})
        
        # Load common configuration
        self.enabled = service_config.get('enabled', True)
        self.monitoring_interval = service_config.get('monitoring_interval', 300)
        
        # Call service-specific config loading
        self._load_service_config(service_config)
        
        logger.info(f"{self.__class__.__name__} configuration loaded from '{self._config_section}' section")
    
    @abstractmethod
    def _load_service_config(self, config: Dict[str, Any]):
        """Override this to load service-specific configuration"""
        pass


def singleton(cls):
    """
    Decorator to create singleton instances with thread safety
    """
    instances = {}
    lock = threading.RLock()
    
    def get_instance(*args, **kwargs):
        if cls not in instances:
            with lock:
                if cls not in instances:
                    instances[cls] = cls(*args, **kwargs)
        return instances[cls]
    
    return get_instance
