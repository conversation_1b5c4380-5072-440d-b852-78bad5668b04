import os
import logging
import yaml
from typing import Dict, Any

logger = logging.getLogger(__name__)
_config_cache = None

def load_config() -> Dict[str, Any]:
    """Load configuration from config.yaml file"""
    global _config_cache
    
    if _config_cache is not None:
        return _config_cache
    
    config_path = os.environ.get('CONFIG_PATH', 'config.yaml')
    
    try:
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        
        _config_cache = config
        return config
    except Exception as e:
        logger.error(f"Error loading config: {e}")
        return {}

def save_config(config: Dict[str, Any]) -> bool:
    """Save configuration to config.yaml file
    
    Args:
        config: The configuration dictionary to save
        
    Returns:
        bool: True if successful, False otherwise
    """
    global _config_cache
    
    config_path = os.environ.get('CONFIG_PATH', 'config.yaml')
    
    try:
        with open(config_path, 'w') as f:
            yaml.dump(config, f, default_flow_style=False)
        
        # Update cache
        _config_cache = config
        
        logger.info("Config saved successfully")
        return True
    except Exception as e:
        logger.error(f"Error saving config: {e}")
        return False

def get_discord_token() -> str:
    """Get Discord bot token from config"""
    config = load_config()
    discord_config = config.get('discord', {})
    return discord_config.get('token', '')

def get_guild_id():
    config = load_config()
    discord_config = config.get('discord', {})
    return discord_config.get('guild_id', '')

def get_admin_id():
    config = load_config()
    discord_config = config.get('discord', {})
    return discord_config.get('admin_id', '')



def get_binance_credentials():
    """Get Binance API credentials from config"""
    config = load_config()
    binance_config = config.get('binance', {})
    return {
        'api_key': binance_config.get('api_key', ''),
        'api_secret': binance_config.get('api_secret', '')
    }

def get_trade_h_credentials():
    """Get Trade-H Binance API credentials from config"""
    config = load_config()
    trade_h_config = config.get('binance_trade_h', {})
    return {
        'api_key': trade_h_config.get('api_key', ''),
        'api_secret': trade_h_config.get('api_secret', ''),
        'testnet': trade_h_config.get('testnet', False),
        'channel_name': trade_h_config.get('channel_name', 'Trade-H'),
        'refresh_interval': trade_h_config.get('refresh_interval', 60)
    }

def get_watchlist_symbols():
    from services.core.symbol_service import is_valid_symbol, normalize_symbol

    config = load_config()
    market_config = config.get('market', {})
    symbols = market_config.get('watchlist_symbols', ['BTCUSDT', 'ETHUSDT'])

    if not isinstance(symbols, list):
        logger.warning("WATCHLIST_SYMBOLS trong config.yaml không phải dạng list. Sử dụng mặc định ['BTCUSDT', 'ETHUSDT'].")
        symbols = ['BTCUSDT', 'ETHUSDT']

    seen = set()
    valid_symbols = []

    for symbol in symbols:
        if not isinstance(symbol, str):
            logger.warning(f"Symbol không phải chuỗi: {symbol}")
            continue

        symbol = normalize_symbol(symbol.strip().replace('/', ''))

        if symbol in seen:
            continue

        seen.add(symbol)

        if is_valid_symbol(symbol):
            valid_symbols.append(symbol)
        else:
            logger.warning(f"Symbol không hợp lệ trong WATCHLIST_SYMBOLS: {symbol}")

    if not valid_symbols:
        logger.warning("Không có symbol hợp lệ nào trong WATCHLIST_SYMBOLS. Sử dụng mặc định ['BTCUSDT', 'ETHUSDT'].")
        valid_symbols = ['BTCUSDT', 'ETHUSDT']

    return valid_symbols

